# iDatas社工系统 Apache配置

# 启用重写引擎
RewriteEngine On

# 防止直接访问敏感文件
<Files "*.json">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

# 防止访问配置文件
<FilesMatch "^(config|includes)/">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# 设置默认首页
DirectoryIndex index.html index.php

# 启用GZIP压缩 - 增强版
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/ld+json
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE text/javascript

    # 排除已经压缩的文件
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|zip|gz|bz2|sit|rar|pdf)$ no-gzip dont-vary
</IfModule>

# 设置缓存 - 增强版
<IfModule mod_expires.c>
    ExpiresActive On

    # 图片文件缓存1年
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"

    # CSS和JavaScript文件缓存1个月
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"

    # 字体文件缓存1年
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
    ExpiresByType font/opentype "access plus 1 year"
    ExpiresByType application/x-font-ttf "access plus 1 year"

    # HTML文件缓存1小时
    ExpiresByType text/html "access plus 1 hour"

    # 其他文件
    ExpiresByType text/plain "access plus 1 week"
    ExpiresByType application/json "access plus 1 hour"

    # 默认缓存
    ExpiresDefault "access plus 1 week"
</IfModule>

# 安全头部和性能优化
<IfModule mod_headers.c>
    # 安全头部
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # 静态资源设置强缓存
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
    </FilesMatch>

    # HTML文件设置协商缓存
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=3600, must-revalidate"
    </FilesMatch>

    # 预加载关键资源
    Header add Link "</assets/css/style.css>; rel=preload; as=style"
    Header add Link "</assets/css/responsive.css>; rel=preload; as=style"
    Header add Link "</assets/js/config.js>; rel=preload; as=script"
    Header add Link "</assets/js/auth.js>; rel=preload; as=script"
    Header add Link "</assets/js/main.js>; rel=preload; as=script"

    # 启用Keep-Alive连接
    Header set Connection keep-alive
</IfModule>

# 防止目录浏览
Options -Indexes

# 文件类型优化
<IfModule mod_mime.c>
    # 添加正确的MIME类型
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
    AddType font/woff .woff
    AddType font/woff2 .woff2
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
</IfModule>

# 启用ETag优化
<IfModule mod_headers.c>
    Header unset ETag
</IfModule>
FileETag None

# 错误页面
ErrorDocument 404 /index.html
ErrorDocument 403 /index.html
ErrorDocument 500 /index.html