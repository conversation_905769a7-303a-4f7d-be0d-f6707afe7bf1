<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iDatas社工系统 - 专业的社工数据查询平台</title>
    <meta name="keywords" content="iDatas社工系统,社工库,数据查询,网红猎魔,白底个户,身份证补齐,综合社工,地区猎魔,二要素核验,档案个户,空号检测,姓名猎魔">
    <meta name="description" content="iDatas社工系统是一个专业的社工数据查询平台，提供网红猎魔、白底个户、身份证补齐等多种查询服务。快速、准确、安全的数据查询解决方案。">
    <meta name="author" content="iDatas Team">
    <meta name="robots" content="index, follow">
    <meta name="language" content="zh-CN">
    <meta name="revisit-after" content="7 days">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.idatas.com/">
    <meta property="og:title" content="iDatas社工系统 - 专业的社工数据查询平台">
    <meta property="og:description" content="iDatas社工系统是一个专业的社工数据查询平台，提供网红猎魔、白底个户、身份证补齐等多种查询服务。快速、准确、安全的数据查询解决方案。">
    <meta property="og:image" content="https://www.idatas.com/assets/images/og-image.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://www.idatas.com/">
    <meta property="twitter:title" content="iDatas社工系统 - 专业的社工数据查询平台">
    <meta property="twitter:description" content="iDatas社工系统是一个专业的社工数据查询平台，提供网红猎魔、白底个户、身份证补齐等多种查询服务。快速、准确、安全的数据查询解决方案。">
    <meta property="twitter:image" content="https://www.idatas.com/assets/images/og-image.png">

    <!-- 移动端优化 -->
    <meta name="theme-color" content="#007AFF">
    <meta name="msapplication-TileColor" content="#007AFF">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="iDatas">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">
    <link rel="manifest" href="manifest.json">

    <!-- 内联关键CSS -->
    <style>
        /* 关键CSS内联 - 首屏渲染必需 */
        *{margin:0;padding:0;box-sizing:border-box}:root{--primary-color:#007AFF;--primary-dark:#0056CC;--bg-primary:#FFFFFF;--bg-secondary:#F2F2F7;--text-primary:#000000;--text-secondary:#3C3C43;--border-color:#E5E5EA;--shadow-light:0 1px 3px rgba(0,0,0,0.1);--radius-medium:12px;--transition-fast:0.2s ease}body{font-family:'Inter',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;background:var(--bg-secondary);color:var(--text-primary);line-height:1.6;font-size:16px;overflow-x:hidden}body:not(.loaded) .tools-section,body:not(.loaded) .announcement-banner{opacity:0;transform:translateY(20px)}body.loaded .tools-section,body.loaded .announcement-banner{opacity:1;transform:translateY(0);transition:opacity 0.3s ease,transform 0.3s ease}.navbar{background:var(--bg-primary);border-bottom:1px solid var(--border-color);position:sticky;top:0;z-index:1000;box-shadow:var(--shadow-light)}.nav-container{max-width:1200px;margin:0 auto;padding:0 20px;display:flex;align-items:center;justify-content:space-between;height:70px}.nav-logo{display:flex;align-items:center;gap:12px;font-weight:700;font-size:24px;color:var(--primary-color)}.logo-img{width:32px;height:32px}.main-content{min-height:calc(100vh - 70px);padding:20px 0}.container{max-width:1200px;margin:0 auto;padding:0 20px}.welcome-section{text-align:center;padding:60px 0;background:var(--bg-primary);border-radius:var(--radius-medium);margin-bottom:40px;box-shadow:var(--shadow-light)}.welcome-title{font-size:2.5rem;font-weight:700;color:var(--text-primary);margin-bottom:16px}.welcome-subtitle{font-size:1.2rem;color:var(--text-secondary);max-width:600px;margin:0 auto}.loading-overlay{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(255,255,255,0.9);display:none;align-items:center;justify-content:center;z-index:9999;backdrop-filter:blur(5px)}.loading-spinner{text-align:center}.spinner{width:40px;height:40px;border:4px solid var(--border-color);border-top:4px solid var(--primary-color);border-radius:50%;animation:spin 1s linear infinite;margin:0 auto 16px}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@media (max-width:768px){.nav-container{padding:0 16px;height:60px}.container{padding:0 16px}.welcome-title{font-size:2rem}.welcome-subtitle{font-size:1.1rem}.welcome-section{padding:40px 0;margin-bottom:30px}}
    </style>

    <!-- 本地图标CSS - 立即加载 -->
    <link rel="stylesheet" href="assets/css/icons.css">

    <!-- 预加载完整CSS资源 -->
    <link rel="preload" href="assets/css/style.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="assets/css/responsive.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript>
        <link rel="stylesheet" href="assets/css/style.css">
        <link rel="stylesheet" href="assets/css/responsive.css">
    </noscript>
    <!-- 预连接到外部资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- 优化字体加载 - 使用font-display: swap -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet"></noscript>

    <!-- FontAwesome 将通过JavaScript异步加载 -->
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <svg class="logo-img" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"/>
                </svg>
                <span class="logo-text">iDatas</span>
            </div>

            <!-- 搜索框 -->
            <div class="nav-search">
                <div class="search-container">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text"
                           class="search-input"
                           placeholder="搜索功能工具..."
                           id="navSearchInput"
                           autocomplete="off">
                    <button class="search-clear" id="searchClear" style="display: none;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <!-- 搜索结果下拉框 -->
                <div class="search-dropdown" id="searchDropdown" style="display: none;">
                    <div class="search-results" id="searchResults">
                        <!-- 搜索结果将动态生成 -->
                    </div>
                </div>
            </div>

            <div class="nav-actions">
                <button class="tutorial-btn" onclick="openTutorial()">
                    <i class="fas fa-play-circle"></i>
                    <span>使用教程</span>
                </button>
                <button class="recharge-btn" onclick="openRecharge()">
                    <i class="fas fa-crown"></i>
                    <span>充值会员</span>
                </button>
                <button class="profile-btn" onclick="openProfile()">
                    <i class="fas fa-user-circle"></i>
                    <span>个人中心</span>
                </button>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
            
            <div class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <div class="container">
            <!-- 公告栏 -->
            <section class="announcement-banner">
                <div class="announcement-container">
                    <div class="announcement-header">
                        <div class="announcement-icon">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <h2 class="announcement-title">📢 平台公告</h2>
                        <button class="announcement-toggle" id="announcementToggle">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                    </div>

                    <div class="announcement-content" id="announcementContent">
                        <div class="announcement-messages">
                            <div class="message-item">
                                <span class="message-icon">🎉</span>
                                <span class="message-text">全新版本,火热上线！！</span>
                            </div>
                            <div class="message-item">
                                <span class="message-icon">🔥</span>
                                <span class="message-text">新人8.8周卡限时体验</span>
                            </div>
                            <div class="message-item">
                                <span class="message-icon">💎</span>
                                <span class="message-text">开通会员享受无限查询 + 专属客服 + 实时数据更新</span>
                            </div>
                            <div class="message-item">
                                <span class="message-icon">⚡</span>
                                <span class="message-text">系统已升级！查询速度提升300%，数据更加精准</span>
                            </div>
                        </div>

                        <div class="announcement-actions">
                            <a href="https://qm.qq.com/q/Xyd5pwZugK" target="_blank" class="action-btn qq-group-btn">
                                <i class="fab fa-qq"></i>
                                <span>官方Q群</span>
                                <div class="btn-shine"></div>
                            </a>

                            <a href="https://api.qnm6.top/" target="_blank" class="action-btn api-doc-btn">
                                <i class="fas fa-code"></i>
                                <span>API文档</span>
                                <div class="btn-shine"></div>
                            </a>

                            <button class="action-btn recharge-btn-announce" onclick="openRecharge()">
                                <i class="fas fa-gem"></i>
                                <span>立即充值</span>
                                <div class="btn-shine"></div>
                            </button>

                            <!--<button class="action-btn vip-btn-announce" onclick="showVipBenefits()">-->
                            <!--    <i class="fas fa-crown"></i>-->
                            <!--    <span>会员特权</span>-->
                            <!--    <div class="btn-shine"></div>-->
                            <!--</button>-->
                        </div>

                        <div class="recharge-incentive">
                            <div class="incentive-content">
                                <div class="incentive-icon">💰</div>
                                <div class="incentive-text">
                                    <h3>限时优惠</h3>
                                    <p>首次充值享8折优惠，开通年费会员更有超值礼包！</p>
                                </div>
                                <button class="incentive-btn" onclick="openRecharge()">
                                    立即享受优惠
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 欢迎区域 -->
            <section class="welcome-section">
                <h1 class="welcome-title">欢迎使用 iDatas 社工系统</h1>
                <p class="welcome-subtitle">专业的社工数据查询平台，快速、准确、安全</p>
            </section>

            <!-- 工具展示区 -->
            <section class="tools-section">
                <div class="section-header">
                    <h2 class="section-title">功能工具</h2>
                    <div class="filter-tabs">
                        <button class="filter-tab active" data-filter="all">全部</button>
                        <button class="filter-tab" data-filter="basic">普通功能</button>
                        <button class="filter-tab" data-filter="vip">VIP功能</button>
                    </div>
                </div>

                <div class="tools-grid" id="toolsGrid">
                    <!-- 工具卡片将通过JavaScript动态生成 -->
                </div>
            </section>
        </div>
    </main>

    <!-- 登录模态框 -->
    <div class="modal" id="loginModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>用户登录</h3>
                <button class="modal-close" onclick="closeModal('loginModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="loginUsername">用户名</label>
                        <input type="text" id="loginUsername" autocomplete="username" required>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">密码</label>
                        <input type="password" id="loginPassword" autocomplete="current-password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">登录</button>
                    <button type="button" class="btn btn-secondary" onclick="showRegister()">注册账号</button>
                </form>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal" id="registerModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>用户注册</h3>
                <button class="modal-close" onclick="closeModal('registerModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="registerForm">
                    <div class="form-group">
                        <label for="registerUsername">用户名</label>
                        <input type="text" id="registerUsername" autocomplete="username" required>
                    </div>
                    <div class="form-group">
                        <label for="registerPassword">密码</label>
                        <input type="password" id="registerPassword" autocomplete="new-password" required>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">确认密码</label>
                        <input type="password" id="confirmPassword" autocomplete="new-password" required>
                    </div>
                    <button type="submit" class="btn btn-primary">注册</button>
                    <button type="button" class="btn btn-secondary" onclick="showLogin()">已有账号</button>
                </form>
            </div>
        </div>
    </div>

    <!-- 工具模态框 -->
    <div class="modal" id="toolModal">
        <div class="modal-content tool-modal-content">
            <div class="modal-header">
                <h3 id="toolModalTitle">工具名称</h3>
                <button class="modal-close" onclick="closeModal('toolModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="toolModalBody">
                <!-- 工具内容将动态加载 -->
            </div>
        </div>
    </div>



    <!-- 个人中心模态框 -->
    <div class="modal" id="profileModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>个人中心</h3>
                <button class="modal-close" onclick="closeModal('profileModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="profileModalBody">
                <!-- 个人信息将动态加载 -->
            </div>
        </div>
    </div>

    <!-- 使用教程模态框 -->
    <div class="modal" id="tutorialModal">
        <div class="modal-content tutorial-modal-content">
            <div class="modal-header">
                <h3>使用教程</h3>
                <button class="modal-close" onclick="closeModal('tutorialModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="video-container">
                    <video controls width="100%" height="400">
                        <source src="" type="video/mp4">
                        您的浏览器不支持视频播放。
                    </video>
                </div>
                <div class="tutorial-description">
                    <h4>使用说明</h4>
                    <p>观看上方视频了解如何使用 iDatas 社工系统的各项功能。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 充值会员模态框 -->
    <div class="modal" id="rechargeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>充值会员</h3>
                <button class="modal-close" onclick="closeModal('rechargeModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="recharge-content">
                    <div class="recharge-info">
                        <div class="vip-benefits">
                            <h4><i class="fas fa-crown"></i> VIP会员特权</h4>
                            <ul>
                                <li><i class="fas fa-check"></i> 解锁所有高级查询工具</li>
                                <li><i class="fas fa-check"></i> 无限制查询次数</li>
                                <li><i class="fas fa-check"></i> 优先技术支持</li>
                                <li><i class="fas fa-check"></i> 专属VIP标识</li>
                            </ul>
                        </div>

                        <div class="recharge-methods">
                            <h4><i class="fas fa-credit-card"></i> 充值方式</h4>

                            <div class="method-card active" id="kamiMethod">
                                <div class="method-header">
                                    <i class="fas fa-ticket-alt"></i>
                                    <span>卡密充值</span>
                                </div>
                                <div class="method-content">
                                    <form id="kamiForm">
                                        <div class="form-group">
                                            <label for="kamiInput">请输入卡密</label>
                                            <input type="text" id="kamiInput" placeholder="请输入您的卡密" required>
                                        </div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-magic"></i>
                                            立即充值
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <div class="method-card">
                                <div class="method-header">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>购买卡密</span>
                                </div>
                                <div class="method-content">
                                    <p>需要购买卡密？选择下方购买方式</p>
                                    <div class="purchase-buttons">
                                        <button class="btn btn-telegram" onclick="buyKami()">
                                            <i class="fab fa-telegram"></i>
                                            Tg机器人购买
                                        </button>
                                        <button class="btn btn-shop" onclick="openOnlineShop()">
                                            <i class="fas fa-store"></i>
                                            在线商城购买
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="recharge-result" id="rechargeResult" style="display: none;">
                        <!-- 充值结果将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>加载中...</p>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <span class="toast-message"></span>
        </div>
    </div>

    <!-- 右上角悬浮会员竖幅 -->
    <div class="floating-vip-banner" id="floatingVipBanner">
        <div class="banner-content" onclick="openRecharge()">
            <div class="banner-icon">
                <i class="fas fa-crown"></i>
            </div>
            <div class="banner-text">
                <div class="banner-title">开通会员</div>
                <div class="banner-subtitle">享8折优惠</div>
            </div>
            <div class="banner-arrow">
                <i class="fas fa-chevron-right"></i>
            </div>
        </div>
        <div class="banner-glow"></div>
        <div class="banner-minimize" onclick="minimizeBanner(event)" title="最小化">
            <i class="fas fa-minus"></i>
        </div>
    </div>

    <!-- 最小化后的小图标 -->
    <div class="floating-vip-mini" id="floatingVipMini" onclick="restoreBanner()" title="开通会员享8折优惠">
        <i class="fas fa-crown"></i>
        <div class="mini-pulse"></div>
    </div>

    <!-- 移动端底栏 -->
    <div class="mobile-bottom-bar" id="mobileBottomBar">
        <div class="bottom-bar-container">
            <button class="bottom-bar-item" onclick="scrollToTop()">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </button>
            <button class="bottom-bar-item" onclick="openTutorial()">
                <i class="fas fa-play-circle"></i>
                <span>教程</span>
            </button>
            <button class="bottom-bar-item recharge" onclick="openRecharge()">
                <i class="fas fa-crown"></i>
                <span>充值</span>
            </button>
            <button class="bottom-bar-item" onclick="openProfile()">
                <i class="fas fa-user-circle"></i>
                <span>个人</span>
            </button>
        </div>
    </div>

    <!-- 结构化数据 -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "iDatas社工系统",
        "description": "专业的社工数据查询平台，提供网红猎魔、白底个户、身份证补齐等多种查询服务",
        "url": "https://www.idatas.com",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "CNY"
        },
        "author": {
            "@type": "Organization",
            "name": "iDatas Team"
        },
        "provider": {
            "@type": "Organization",
            "name": "iDatas",
            "url": "https://www.idatas.com"
        }
    }
    </script>

    <!-- 预加载关键JavaScript资源 -->
    <link rel="preload" href="assets/js/config.js" as="script">
    <link rel="preload" href="assets/js/auth.js" as="script">
    <link rel="preload" href="assets/js/main.js" as="script">

    <!-- 性能优化脚本 - 最先加载 -->
    <script src="assets/js/performance.js"></script>

    <!-- 关键JavaScript文件 - 立即加载 -->
    <script src="assets/js/config.js"></script>
    <script src="assets/js/auth.js"></script>
    <script src="assets/js/main.js"></script>

    <!-- 非关键JavaScript文件 - 延迟加载 -->
    <script defer src="assets/js/tools.js"></script>
    <script defer src="assets/js/search.js"></script>
    <script defer src="assets/js/ui.js"></script>
    <script defer src="assets/js/recharge.js"></script>

    <!-- 资源加载管理器 -->
    <script>
        // 资源加载管理器
        const ResourceLoader = {
            loaded: new Set(),

            // 异步加载CSS
            loadCSS: function(url, id) {
                if (this.loaded.has(id) || document.querySelector(`link[href="${url}"]`)) {
                    return Promise.resolve();
                }

                return new Promise((resolve, reject) => {
                    const link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = url;
                    link.crossOrigin = 'anonymous';
                    link.onload = () => {
                        this.loaded.add(id);
                        resolve();
                    };
                    link.onerror = reject;
                    document.head.appendChild(link);
                });
            },

            // 异步加载JavaScript
            loadJS: function(url, id) {
                if (this.loaded.has(id) || document.querySelector(`script[src="${url}"]`)) {
                    return Promise.resolve();
                }

                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = url;
                    script.async = true;
                    script.onload = () => {
                        this.loaded.add(id);
                        resolve();
                    };
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            }
        };

        // 按需加载函数
        window.loadQRCode = function() {
            return ResourceLoader.loadJS('https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js', 'qrcode');
        };

        window.loadHLS = function() {
            return ResourceLoader.loadJS('https://cdn.jsdelivr.net/npm/hls.js@latest', 'hls');
        };

        window.loadFontAwesome = function() {
            return ResourceLoader.loadCSS('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', 'fontawesome');
        };

        // 优化的页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 移除预加载类，启用动画
            document.body.classList.add('loaded');
        });

        // 页面完全加载后的非关键资源加载
        window.addEventListener('load', function() {
            // 延迟加载FontAwesome，仅在需要时加载
            setTimeout(() => {
                // 检查页面是否真的需要FontAwesome图标
                if (document.querySelector('.fa, .fas, .far, .fab')) {
                    loadFontAwesome();
                }
            }, 500);

            // 注册Service Worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('Service Worker registered successfully:', registration.scope);
                    })
                    .catch(error => {
                        console.log('Service Worker registration failed:', error);
                    });
            }
        });
    </script>
    <script> var _mtj = _mtj || []; (function () { var mtj = document.createElement("script"); mtj.src = "https://node93.aizhantj.com:21233/tjjs/?k=wi7nbpm2pux"; var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(mtj, s); })(); </script>
</body>
</html>

