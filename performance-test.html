<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能测试页面 - iDatas优化验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007AFF;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        .error {
            background: #ffe8e8;
            color: #d63384;
        }
        .success {
            background: #e8f5e8;
            color: #198754;
        }
        .warning {
            background: #fff3cd;
            color: #664d03;
        }
        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056CC;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #007AFF;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>🚀 iDatas 性能优化测试</h1>
    
    <div class="test-section">
        <h2>📊 页面性能指标</h2>
        <div class="metrics" id="performanceMetrics">
            <!-- 性能指标将动态填充 -->
        </div>
        <button onclick="measurePerformance()">测量性能</button>
        <button onclick="clearResults()">清除结果</button>
    </div>

    <div class="test-section">
        <h2>🔍 资源加载测试</h2>
        <p>测试各种资源的加载时间和状态</p>
        <button onclick="testResourceLoading()">测试资源加载</button>
        <div id="resourceResults"></div>
    </div>

    <div class="test-section">
        <h2>⚡ 缓存测试</h2>
        <p>测试Service Worker和浏览器缓存</p>
        <button onclick="testCaching()">测试缓存</button>
        <button onclick="clearCache()">清除缓存</button>
        <div id="cacheResults"></div>
    </div>

    <div class="test-section">
        <h2>📱 网络条件模拟</h2>
        <p>模拟不同网络条件下的加载表现</p>
        <button onclick="simulateSlowNetwork()">模拟慢速网络</button>
        <button onclick="simulateFastNetwork()">模拟快速网络</button>
        <div id="networkResults"></div>
    </div>

    <div class="test-section">
        <h2>🎯 优化建议</h2>
        <div id="recommendations"></div>
        <button onclick="generateRecommendations()">生成建议</button>
    </div>

    <script>
        // 性能测量
        function measurePerformance() {
            const metrics = {};
            
            if (window.performance && window.performance.timing) {
                const timing = window.performance.timing;
                
                metrics.pageLoad = timing.loadEventEnd - timing.navigationStart;
                metrics.domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
                metrics.firstPaint = timing.responseStart - timing.navigationStart;
                metrics.domInteractive = timing.domInteractive - timing.navigationStart;
            }
            
            // Web Vitals 模拟
            if (window.performance && window.performance.getEntriesByType) {
                const paintEntries = window.performance.getEntriesByType('paint');
                paintEntries.forEach(entry => {
                    if (entry.name === 'first-contentful-paint') {
                        metrics.fcp = Math.round(entry.startTime);
                    }
                });
            }
            
            displayMetrics(metrics);
        }
        
        function displayMetrics(metrics) {
            const container = document.getElementById('performanceMetrics');
            container.innerHTML = '';
            
            Object.entries(metrics).forEach(([key, value]) => {
                const metricDiv = document.createElement('div');
                metricDiv.className = 'metric';
                
                const valueDiv = document.createElement('div');
                valueDiv.className = 'metric-value';
                valueDiv.textContent = value ? `${value}ms` : 'N/A';
                
                const labelDiv = document.createElement('div');
                labelDiv.className = 'metric-label';
                labelDiv.textContent = getMetricLabel(key);
                
                metricDiv.appendChild(valueDiv);
                metricDiv.appendChild(labelDiv);
                container.appendChild(metricDiv);
            });
        }
        
        function getMetricLabel(key) {
            const labels = {
                pageLoad: '页面加载时间',
                domReady: 'DOM就绪时间',
                firstPaint: '首次绘制',
                domInteractive: 'DOM交互时间',
                fcp: '首次内容绘制'
            };
            return labels[key] || key;
        }
        
        // 资源加载测试
        function testResourceLoading() {
            const results = document.getElementById('resourceResults');
            results.innerHTML = '<div class="result">正在测试资源加载...</div>';
            
            const resources = [
                { name: 'FontAwesome CSS', url: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' },
                { name: 'Google Fonts', url: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap' },
                { name: 'QRCode JS', url: 'https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js' },
                { name: 'HLS.js', url: 'https://cdn.jsdelivr.net/npm/hls.js@latest' }
            ];
            
            const testPromises = resources.map(resource => testResourceLoad(resource));
            
            Promise.allSettled(testPromises).then(results => {
                displayResourceResults(results, resources);
            });
        }
        
        function testResourceLoad(resource) {
            return new Promise((resolve, reject) => {
                const startTime = performance.now();
                const img = new Image();
                
                img.onload = () => {
                    const loadTime = performance.now() - startTime;
                    resolve({ ...resource, loadTime, status: 'success' });
                };
                
                img.onerror = () => {
                    const loadTime = performance.now() - startTime;
                    resolve({ ...resource, loadTime, status: 'error' });
                };
                
                // 对于CSS和JS，使用fetch测试
                if (resource.url.includes('.css') || resource.url.includes('.js')) {
                    fetch(resource.url, { mode: 'no-cors' })
                        .then(() => {
                            const loadTime = performance.now() - startTime;
                            resolve({ ...resource, loadTime, status: 'success' });
                        })
                        .catch(() => {
                            const loadTime = performance.now() - startTime;
                            resolve({ ...resource, loadTime, status: 'error' });
                        });
                } else {
                    img.src = resource.url;
                }
            });
        }
        
        function displayResourceResults(results, resources) {
            const container = document.getElementById('resourceResults');
            container.innerHTML = '';
            
            results.forEach((result, index) => {
                const div = document.createElement('div');
                div.className = `result ${result.value.status}`;
                
                const resource = result.value;
                div.innerHTML = `
                    <strong>${resource.name}</strong><br>
                    加载时间: ${Math.round(resource.loadTime)}ms<br>
                    状态: ${resource.status === 'success' ? '✅ 成功' : '❌ 失败'}
                `;
                
                container.appendChild(div);
            });
        }
        
        // 缓存测试
        function testCaching() {
            const results = document.getElementById('cacheResults');
            results.innerHTML = '<div class="result">正在测试缓存...</div>';
            
            // 测试Service Worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(registrations => {
                    const swStatus = registrations.length > 0 ? '✅ Service Worker已注册' : '❌ Service Worker未注册';
                    
                    // 测试Cache API
                    if ('caches' in window) {
                        caches.keys().then(cacheNames => {
                            const cacheStatus = cacheNames.length > 0 ? `✅ 发现${cacheNames.length}个缓存` : '❌ 未发现缓存';
                            
                            results.innerHTML = `
                                <div class="result success">${swStatus}</div>
                                <div class="result success">${cacheStatus}</div>
                                <div class="result">缓存名称: ${cacheNames.join(', ')}</div>
                            `;
                        });
                    } else {
                        results.innerHTML = `
                            <div class="result success">${swStatus}</div>
                            <div class="result error">❌ Cache API不支持</div>
                        `;
                    }
                });
            } else {
                results.innerHTML = '<div class="result error">❌ Service Worker不支持</div>';
            }
        }
        
        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(cacheNames => {
                    return Promise.all(
                        cacheNames.map(cacheName => caches.delete(cacheName))
                    );
                }).then(() => {
                    document.getElementById('cacheResults').innerHTML = 
                        '<div class="result success">✅ 缓存已清除</div>';
                });
            }
        }
        
        // 网络模拟
        function simulateSlowNetwork() {
            // 这里只是模拟，实际需要浏览器开发者工具
            document.getElementById('networkResults').innerHTML = 
                '<div class="result warning">⚠️ 请在浏览器开发者工具中设置网络限制来测试慢速网络</div>';
        }
        
        function simulateFastNetwork() {
            document.getElementById('networkResults').innerHTML = 
                '<div class="result success">✅ 当前为正常网络速度</div>';
        }
        
        // 生成优化建议
        function generateRecommendations() {
            const recommendations = [
                '✅ 已实现关键CSS内联',
                '✅ 已实现资源预加载',
                '✅ 已实现JavaScript延迟加载',
                '✅ 已实现Service Worker缓存',
                '✅ 已优化字体加载策略',
                '🔄 建议启用HTTP/2',
                '🔄 建议使用CDN加速',
                '🔄 建议压缩图片为WebP格式',
                '🔄 建议启用Brotli压缩'
            ];
            
            const container = document.getElementById('recommendations');
            container.innerHTML = recommendations.map(rec => 
                `<div class="result">${rec}</div>`
            ).join('');
        }
        
        function clearResults() {
            document.getElementById('performanceMetrics').innerHTML = '';
            document.getElementById('resourceResults').innerHTML = '';
            document.getElementById('cacheResults').innerHTML = '';
            document.getElementById('networkResults').innerHTML = '';
            document.getElementById('recommendations').innerHTML = '';
        }
        
        // 页面加载时自动测量性能
        window.addEventListener('load', function() {
            setTimeout(measurePerformance, 100);
        });
    </script>
</body>
</html>
